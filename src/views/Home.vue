<template>
  <div class="home" :class="{ loaded: isLoaded }">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="hero-particles">
          <div v-for="i in 6" :key="i" :class="`particle particle-${i}`"></div>
        </div>
      </div>

      <div class="container">
        <div class="hero-content">
          <div class="hero-badge">
            <Icon icon="mdi:water" class="badge-icon" />
            <span>智慧水利领域专家</span>
          </div>

          <h1 class="hero-title">
            <span class="title-line">
              <img
                src="@/assets/images/home-title-left.webp"
                alt="智慧水生态"
                class="title-image fade-in-up"
              />
            </span>
            <span class="title-line">
              <img
                src="@/assets/images/home-title-right.webp"
                alt="数智新未来"
                class="title-image fade-in-up delay-1"
              />
            </span>
          </h1>

          <p class="hero-subtitle fade-in-up delay-2">
            基于物联网、大数据、AI等前沿技术，为水利行业提供<br />
            <strong>全方位数字化转型解决方案</strong>
          </p>

          <div class="hero-stats fade-in-up delay-3">
            <div class="stat-item">
              <div class="stat-number">500+</div>
              <div class="stat-label">成功项目</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-number">50+</div>
              <div class="stat-label">合作伙伴</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-number">15年</div>
              <div class="stat-label">行业经验</div>
            </div>
          </div>

          <div class="hero-actions fade-in-up delay-4">
            <button class="btn-primary" @click="handleExplore">
              <Icon icon="mdi:rocket-launch" class="btn-icon" />
              <span>探索解决方案</span>
              <Icon icon="mdi:arrow-right" class="btn-arrow" />
            </button>
            <button class="btn-secondary" @click="handleContact">
              <Icon icon="mdi:phone" class="btn-icon" />
              <span>联系我们</span>
            </button>
          </div>
        </div>

        <div class="hero-visual fade-in-up delay-5">
          <div class="visual-container">
            <div class="tech-rings">
              <div class="ring ring-1"></div>
              <div class="ring ring-2"></div>
              <div class="ring ring-3"></div>
            </div>
            <div class="center-logo">
              <Icon icon="mdi:water-circle" class="logo-icon" />
            </div>
            <div class="floating-icons">
              <div class="floating-icon icon-1">
                <Icon icon="mdi:chart-line" />
              </div>
              <div class="floating-icon icon-2">
                <Icon icon="mdi:cloud-outline" />
              </div>
              <div class="floating-icon icon-3">
                <Icon icon="mdi:shield-check" />
              </div>
              <div class="floating-icon icon-4">
                <Icon icon="mdi:trending-up" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="scroll-indicator">
        <div class="scroll-dot"></div>
        <div class="scroll-text">滚动了解更多</div>
      </div>
    </section>

    <!-- 核心优势 -->
    <section class="advantages-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">为什么选择怀川科技</h2>
          <p class="section-subtitle">专业实力 × 创新技术 × 贴心服务</p>
        </div>

        <div class="advantages-grid">
          <div
            class="advantage-card"
            v-for="(advantage, index) in advantages"
            :key="index"
          >
            <div class="card-icon">
              <Icon :icon="advantage.icon" />
            </div>
            <h3 class="card-title">{{ advantage.title }}</h3>
            <p class="card-description">{{ advantage.description }}</p>
            <div class="card-features">
              <div
                class="feature"
                v-for="feature in advantage.features"
                :key="feature"
              >
                <Icon icon="mdi:check-circle" class="feature-icon" />
                <span>{{ feature }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 产品展示 -->
    <section class="products-preview">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">核心产品</h2>
          <p class="section-subtitle">覆盖水利管理全生命周期的智能化产品矩阵</p>
        </div>

        <div class="products-showcase">
          <div
            class="product-card featured"
            v-for="(product, index) in featuredProducts"
            :key="index"
          >
            <div class="product-visual">
              <div class="product-icon">
                <Icon :icon="product.icon" />
              </div>
              <div class="product-badge">{{ product.badge }}</div>
            </div>
            <div class="product-content">
              <h3 class="product-title">{{ product.title }}</h3>
              <p class="product-description">{{ product.description }}</p>
              <div class="product-tags">
                <span class="tag" v-for="tag in product.tags" :key="tag">{{
                  tag
                }}</span>
              </div>
              <button class="product-btn">
                <span>了解详情</span>
                <Icon icon="mdi:arrow-right" />
              </button>
            </div>
          </div>
        </div>

        <div class="section-footer">
          <button class="btn-outline" @click="handleProducts">
            <span>查看全部产品</span>
            <Icon icon="mdi:arrow-right" class="btn-arrow" />
          </button>
        </div>
      </div>
    </section>

    <!-- CTA 区域 -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <h2 class="cta-title">准备开始您的数字化转型之旅？</h2>
            <p class="cta-subtitle">
              让我们的专家团队为您量身定制最适合的智慧水利解决方案
            </p>
          </div>
          <div class="cta-actions">
            <button class="btn-primary large" @click="handleContact">
              <Icon icon="mdi:account-tie" class="btn-icon" />
              <span>预约专家咨询</span>
            </button>
            <div class="contact-info">
              <div class="contact-item">
                <Icon icon="mdi:phone" />
                <span>************</span>
              </div>
              <div class="contact-item">
                <Icon icon="mdi:email" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";
import { Icon } from "@iconify/vue";
import { onMounted, ref } from "vue";

defineOptions({
  name: "Home",
});

const router = useRouter();
const isLoaded = ref(false);

// 页面数据
const advantages = ref([
  {
    icon: "mdi:lightbulb-on",
    title: "技术创新",
    description: "持续投入研发，掌握核心技术",
    features: ["自主知识产权", "前沿技术栈", "持续创新"],
  },
  {
    icon: "mdi:account-group",
    title: "专业团队",
    description: "15年行业经验的专家团队",
    features: ["资深专家", "专业服务", "技术支持"],
  },
  {
    icon: "mdi:shield-check",
    title: "可靠保障",
    description: "全方位的质量和安全保障",
    features: ["质量认证", "安全可靠", "售后保障"],
  },
  {
    icon: "mdi:chart-line-variant",
    title: "显著效果",
    description: "帮助客户实现数字化转型",
    features: ["效率提升", "成本降低", "智能决策"],
  },
]);

const featuredProducts = ref([
  {
    icon: "mdi:water-pump",
    title: "智慧水务管理平台",
    description: "集成物联网传感器、大数据分析的一体化水务管理解决方案",
    badge: "核心产品",
    tags: ["物联网", "大数据", "智能监控"],
  },
  {
    icon: "mdi:weather-flood",
    title: "防汛预警系统",
    description: "基于AI算法的实时监测预警系统，保障人民生命财产安全",
    badge: "重点推荐",
    tags: ["AI预警", "实时监测", "安全保障"],
  },
  {
    icon: "mdi:chart-multiple",
    title: "水资源优化配置",
    description: "通过大数据分析优化水资源配置，提高利用效率",
    badge: "智能优化",
    tags: ["资源优化", "数据分析", "效率提升"],
  },
]);

// 页面加载动画
onMounted(() => {
  setTimeout(() => {
    isLoaded.value = true;
  }, 100);
});

// 事件处理
const handleExplore = () => {
  router.push("/solutions");
};

const handleContact = () => {
  // 这里可以添加联系我们的逻辑，比如弹窗或跳转
  console.log("联系我们");
};

const handleProducts = () => {
  router.push("/products");
};
</script>
<style lang="scss" scoped>
@use "@/assets/styles/variables.scss" as *;

.home {
  min-height: 100vh;
  color: var(--gray-800);
  overflow-x: hidden;

  // 页面加载动画
  opacity: 0;
  transition: opacity var(--duration-700) var(--ease-out);

  &.loaded {
    opacity: 1;

    .fade-in-up {
      opacity: 1;
      transform: translateY(0);

      &.delay-1 {
        animation-delay: 0.1s;
      }
      &.delay-2 {
        animation-delay: 0.2s;
      }
      &.delay-3 {
        animation-delay: 0.3s;
      }
      &.delay-4 {
        animation-delay: 0.4s;
      }
      &.delay-5 {
        animation-delay: 0.5s;
      }
    }
  }
}

// 通用动画类
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s var(--ease-smooth) forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 通用容器
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);

  @media (max-width: 768px) {
    padding: 0 var(--space-4);
  }
}

// 英雄区域
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;

  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-depth);
    z-index: -1;
  }

  .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--primary-alpha-90) 0%,
      var(--corporate-blue) 100%
    );
    z-index: -1;
  }

  .hero-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .particle {
      position: absolute;
      background: var(--gradient-neon);
      border-radius: 50%;
      opacity: 0.6;

      &.particle-1 {
        width: 4px;
        height: 4px;
        top: 20%;
        left: 10%;
        @include floating-animation(3s, 15px, 0s);
      }

      &.particle-2 {
        width: 6px;
        height: 6px;
        top: 60%;
        left: 80%;
        @include floating-animation(4s, 20px, 0.5s);
      }

      &.particle-3 {
        width: 3px;
        height: 3px;
        top: 80%;
        left: 20%;
        @include floating-animation(5s, 10px, 1s);
      }

      &.particle-4 {
        width: 5px;
        height: 5px;
        top: 30%;
        left: 70%;
        @include floating-animation(3.5s, 18px, 1.5s);
      }

      &.particle-5 {
        width: 4px;
        height: 4px;
        top: 50%;
        left: 5%;
        @include floating-animation(4.5s, 12px, 2s);
      }

      &.particle-6 {
        width: 7px;
        height: 7px;
        top: 10%;
        left: 60%;
        @include floating-animation(3.2s, 25px, 2.5s);
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: var(--space-12);
      text-align: center;
    }
  }

  .hero-content {
    color: var(--white);
  }

  .hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: var(--primary-alpha-20);
    border: 1px solid var(--primary-alpha-30);
    border-radius: var(--radius-2xl);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    margin-bottom: var(--space-6);
    @include glass-morphism(0.1, 12px, 0.3);

    .badge-icon {
      color: var(--electric-blue);
      font-size: var(--text-base);
    }
  }

  .hero-title {
    margin-bottom: var(--space-8);

    .title-line {
      display: block;
      margin-bottom: var(--space-4);

      &:last-child {
        margin-bottom: 0;
      }
    }

    .title-image {
      height: 80px;
      filter: drop-shadow(0 4px 12px var(--primary-alpha-40));
      transition: var(--transition-all);

      &:hover {
        transform: scale(1.05);
        filter: drop-shadow(0 6px 16px var(--primary-alpha-50));
      }
    }
  }

  .hero-subtitle {
    font-size: var(--text-xl);
    line-height: var(--leading-relaxed);
    color: var(--sky-blue);
    margin-bottom: var(--space-8);

    strong {
      color: var(--white);
      @include gradient-text(var(--gradient-neon));
    }
  }

  .hero-stats {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    margin-bottom: var(--space-10);

    @media (max-width: 640px) {
      flex-direction: column;
      gap: var(--space-4);
    }

    .stat-item {
      text-align: center;

      .stat-number {
        font-size: var(--text-3xl);
        font-weight: var(--font-bold);
        color: var(--electric-blue);
        margin-bottom: var(--space-1);
      }

      .stat-label {
        font-size: var(--text-sm);
        color: var(--sky-blue);
        font-weight: var(--font-medium);
      }
    }

    .stat-divider {
      width: 1px;
      height: 40px;
      background: var(--primary-alpha-30);

      @media (max-width: 640px) {
        width: 40px;
        height: 1px;
      }
    }
  }

  .hero-actions {
    display: flex;
    gap: var(--space-4);

    @media (max-width: 640px) {
      flex-direction: column;
      align-items: center;
    }
  }

  .btn-primary,
  .btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-4) var(--space-6);
    border-radius: var(--radius-2xl);
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: var(--transition-all);
    position: relative;
    overflow: hidden;

    .btn-icon {
      font-size: var(--text-lg);
    }

    .btn-arrow {
      transition: var(--transition-all);
    }

    &:hover .btn-arrow {
      transform: translateX(4px);
    }
  }

  .btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-lg);

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-hover);
    }

    &.large {
      padding: var(--space-5) var(--space-8);
      font-size: var(--text-lg);
    }
  }

  .btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--primary-alpha-40);
    @include glass-morphism(0.1, 12px, 0.3);

    &:hover {
      background: var(--primary-alpha-20);
      border-color: var(--primary-alpha-60);
      transform: translateY(-1px);
    }
  }

  // 视觉区域
  .hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 500px;

    @media (max-width: 968px) {
      height: 300px;
    }
  }

  .visual-container {
    position: relative;
    width: 400px;
    height: 400px;

    @media (max-width: 968px) {
      width: 250px;
      height: 250px;
    }
  }

  .tech-rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .ring {
      position: absolute;
      border: 2px solid;
      border-radius: 50%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      &.ring-1 {
        width: 150px;
        height: 150px;
        border-color: var(--tech-alpha-30);
        animation: rotate 20s linear infinite;
      }

      &.ring-2 {
        width: 250px;
        height: 250px;
        border-color: var(--primary-alpha-20);
        animation: rotate 30s linear infinite reverse;
      }

      &.ring-3 {
        width: 350px;
        height: 350px;
        border-color: var(--electric-blue);
        opacity: 0.3;
        animation: rotate 40s linear infinite;
      }
    }
  }

  .center-logo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .logo-icon {
      font-size: 80px;
      color: var(--electric-blue);
      filter: drop-shadow(0 0 20px var(--electric-blue));
      @include floating-animation(3s, 10px);
    }
  }

  .floating-icons {
    .floating-icon {
      position: absolute;
      width: 40px;
      height: 40px;
      background: var(--gradient-glass);
      @include glass-morphism(0.2, 12px, 0.3);
      border-radius: var(--radius-xl);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--electric-blue);
      font-size: var(--text-xl);

      &.icon-1 {
        top: 10%;
        left: 20%;
        @include floating-animation(4s, 15px, 0s);
      }

      &.icon-2 {
        top: 20%;
        right: 15%;
        @include floating-animation(3.5s, 12px, 0.5s);
      }

      &.icon-3 {
        bottom: 25%;
        left: 10%;
        @include floating-animation(4.2s, 18px, 1s);
      }

      &.icon-4 {
        bottom: 15%;
        right: 20%;
        @include floating-animation(3.8s, 14px, 1.5s);
      }
    }
  }

  // 滚动指示器
  .scroll-indicator {
    position: absolute;
    bottom: var(--space-8);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
    color: var(--sky-blue);

    .scroll-dot {
      width: 6px;
      height: 6px;
      background: var(--electric-blue);
      border-radius: 50%;
      animation: bounce 2s infinite;
    }

    .scroll-text {
      font-size: var(--text-xs);
      font-weight: var(--font-medium);
    }
  }
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

// 通用区域样式
.section-header {
  text-align: center;
  margin-bottom: var(--space-16);

  .section-title {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
    color: var(--gray-900);
    margin-bottom: var(--space-4);
    @include gradient-text(var(--gradient-primary));
  }

  .section-subtitle {
    font-size: var(--text-lg);
    color: var(--gray-600);
    font-weight: var(--font-medium);
  }
}

.section-footer {
  text-align: center;
  margin-top: var(--space-12);

  .btn-outline {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    background: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
    border-radius: var(--radius-2xl);
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: var(--transition-all);

    .btn-arrow {
      transition: var(--transition-all);
    }

    &:hover {
      background: var(--primary-blue);
      color: var(--white);
      transform: translateY(-2px);
      box-shadow: var(--shadow-hover);

      .btn-arrow {
        transform: translateX(4px);
      }
    }
  }
}

// 核心优势区域
.advantages-section {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--gradient-subtle);

  .advantages-grid {
    @include responsive-grid(auto-fit, 280px, var(--space-8));
  }

  .advantage-card {
    background: var(--white);
    padding: var(--space-8);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-all);
    border: 1px solid var(--gray-200);

    @include card-hover(1.03, -6px);

    .card-icon {
      width: 60px;
      height: 60px;
      background: var(--gradient-ocean);
      border-radius: var(--radius-xl);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: var(--space-6);
      color: var(--white);
      font-size: var(--text-2xl);
      box-shadow: var(--glow-primary);
    }

    .card-title {
      font-size: var(--text-xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-3);
    }

    .card-description {
      color: var(--gray-600);
      line-height: var(--leading-relaxed);
      margin-bottom: var(--space-6);
    }

    .card-features {
      display: flex;
      flex-direction: column;
      gap: var(--space-2);

      .feature {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        font-size: var(--text-sm);
        color: var(--gray-700);

        .feature-icon {
          color: var(--success-green);
          font-size: var(--text-base);
        }
      }
    }
  }
}

// 产品展示区域
.products-preview {
  @include section-padding(var(--space-20), var(--space-20));

  .products-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-8);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .product-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-all);
    border: 1px solid var(--gray-200);

    @include card-hover(1.02, -4px);

    .product-visual {
      position: relative;
      padding: var(--space-8);
      background: var(--gradient-subtle);
      text-align: center;

      .product-icon {
        width: 80px;
        height: 80px;
        background: var(--gradient-primary);
        border-radius: var(--radius-2xl);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--space-4);
        color: var(--white);
        font-size: var(--text-3xl);
        box-shadow: var(--glow-primary);
      }

      .product-badge {
        position: absolute;
        top: var(--space-4);
        right: var(--space-4);
        background: var(--gradient-innovation);
        color: var(--white);
        padding: var(--space-1) var(--space-3);
        border-radius: var(--radius-2xl);
        font-size: var(--text-xs);
        font-weight: var(--font-medium);
      }
    }

    .product-content {
      padding: var(--space-6);

      .product-title {
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--gray-900);
        margin-bottom: var(--space-3);
      }

      .product-description {
        color: var(--gray-600);
        line-height: var(--leading-relaxed);
        margin-bottom: var(--space-4);
      }

      .product-tags {
        display: flex;
        flex-wrap: wrap;
        gap: var(--space-2);
        margin-bottom: var(--space-6);

        .tag {
          background: var(--crystal-blue);
          color: var(--primary-blue);
          padding: var(--space-1) var(--space-3);
          border-radius: var(--radius-2xl);
          font-size: var(--text-xs);
          font-weight: var(--font-medium);
        }
      }

      .product-btn {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        background: transparent;
        color: var(--primary-blue);
        border: 2px solid var(--gray-200);
        border-radius: var(--radius-lg);
        padding: var(--space-3) var(--space-4);
        font-weight: var(--font-medium);
        cursor: pointer;
        transition: var(--transition-all);

        &:hover {
          border-color: var(--primary-blue);
          background: var(--primary-alpha-5);
        }
      }
    }
  }
}

// CTA 区域
.cta-section {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--gradient-primary);
  color: var(--white);

  .cta-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--space-12);
    align-items: center;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      text-align: center;
      gap: var(--space-8);
    }
  }

  .cta-title {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    margin-bottom: var(--space-4);
  }

  .cta-subtitle {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    opacity: 0.9;
  }

  .cta-actions {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    align-items: flex-end;

    @media (max-width: 968px) {
      align-items: center;
    }

    .contact-info {
      display: flex;
      gap: var(--space-6);

      @media (max-width: 640px) {
        flex-direction: column;
        gap: var(--space-2);
      }

      .contact-item {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        font-size: var(--text-sm);
        opacity: 0.8;
      }
    }
  }
}

// 响应式适配
@media (max-width: 968px) {
  .hero-section .container {
    .hero-content,
    .hero-visual {
      grid-column: 1;
    }

    .hero-visual {
      order: -1;
    }
  }
}

@media (max-width: 640px) {
  .hero-title .title-image {
    height: 60px;
  }

  .hero-subtitle {
    font-size: var(--text-lg);
  }

  .section-header .section-title {
    font-size: var(--text-3xl);
  }

  .advantage-card,
  .product-card {
    margin: 0 var(--space-2);
  }
}
</style>
