<script setup>
import { Icon } from "@iconify/vue";
import { ref, onMounted } from "vue";

defineOptions({
  name: "About",
});

// 页面加载动画
const isLoaded = ref(false);

onMounted(() => {
  // 设置页面已加载
  setTimeout(() => {
    isLoaded.value = true;
  }, 300);
});
</script>

<template>
  <div class="about" :class="{ 'is-loaded': isLoaded }">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="container">
        <h1>关于我们</h1>
        <p class="slogan">怀数智之力 护青绿山川</p>
        <div class="water-wave"></div>
      </div>
    </div>

    <!-- 公司介绍 -->
    <section class="company-intro">
      <div class="container">
        <div class="intro-content">
          <div class="intro-text">
            <h2>怀川科技简介</h2>
            <p>
              怀川科技（北京）有限公司，是一家专注于智慧水利行业的现代化科技型企业，主要面向水利行业管理部门用户，提供防洪减灾、水资源管理、水利工程运行管理、河湖管理、水环境保护等业务领域数字化建设相关的规划咨询、产品销售、软件研发和系统集成相关专业服务。
            </p>
            <p>
              公司总部设置在北京，研发中心在江苏，成员主要毕业于河海大学等水利或计算机相关院校和专业，多人具有高级项目管理师资格，有丰富的水利信息化项目管理和实施经验。
            </p>
            <p>
              公司注重锐意革新，致力于利用先进信息技术包括AI大模型、大数据、云计算、卫星遥感、无人机&船等，提升水利行业数字化建设专业服务水平，促进人与自然和谐发展，已于2024年初获得中关村高新技术企业证书，并取得10余项相关软著。
            </p>
          </div>
          <div class="intro-image">
            <div class="tech-elements">
              <div class="floating-icon" style="--delay: 0s">
                <Icon icon="mdi:water" />
              </div>
              <div class="floating-icon" style="--delay: 1s">
                <Icon icon="mdi:cloud-outline" />
              </div>
              <div class="floating-icon" style="--delay: 2s">
                <Icon icon="mdi:chip" />
              </div>
              <div class="floating-icon" style="--delay: 3s">
                <Icon icon="mdi:satellite-variant" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 资质荣誉 -->
    <section class="certificates">
      <div class="container">
        <h2 class="section-title">资质荣誉</h2>
        <div class="cert-grid">
          <div class="cert-item">
            <div class="cert-icon">
              <Icon icon="mdi:certificate" />
            </div>
            <h3>中关村高新技术企业</h3>
            <p>2024年初获得认证</p>
          </div>
          <div class="cert-item">
            <div class="cert-icon">
              <Icon icon="mdi:file-document" />
            </div>
            <h3>软件著作权</h3>
            <p>已取得10余项相关软著</p>
          </div>
          <div class="cert-item">
            <div class="cert-icon">
              <Icon icon="mdi:account-tie" />
            </div>
            <h3>高级项目管理师</h3>
            <p>多人具有专业资格认证</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 愿景使命 -->
    <section class="vision-mission">
      <div class="container">
        <div class="vision">
          <h2><Icon icon="mdi:eye" class="section-icon" /> 我们的愿景</h2>
          <p>
            成为智慧水利领域的领军企业，推动水利行业数字化转型，促进人与自然和谐发展。
          </p>
        </div>
        <div class="mission">
          <h2><Icon icon="mdi:flag" class="section-icon" /> 我们的使命</h2>
          <p>
            怀数智之力，护青绿山川，以科技创新驱动水利行业发展，构建水资源智能管理体系。
          </p>
        </div>
      </div>
    </section>

    <!-- 联系我们 -->
    <section class="contact-us">
      <div class="container">
        <h2 class="section-title">联系我们</h2>
        <div class="contact-info">
          <div class="contact-item">
            <Icon icon="mdi:map-marker" class="contact-icon" />
            <div>
              <h3>公司总部</h3>
              <p>北京市海淀区中关村科技园</p>
            </div>
          </div>
          <div class="contact-item">
            <Icon icon="mdi:cog" class="contact-icon" />
            <div>
              <h3>研发中心</h3>
              <p>江苏省无锡市滨湖区雪浪小镇</p>
            </div>
          </div>
          <div class="contact-item">
            <Icon icon="mdi:email" class="contact-icon" />
            <div>
              <h3>电子邮箱</h3>
              <p><EMAIL></p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
// 科技蓝海配色方案
$primary-blue: #1565c0;
$ocean-blue: #1976d2;
$accent-teal: #00695c;
$water-blue: #42a5f5;
$tech-blue: #1e88e5;
$bg-light: #f8fffe;

.about {
  height: auto;
  overflow-y: auto;
  // background: linear-gradient(135deg, $bg-light 0%, #e8f5e8 100%);

  &::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-image: url("@/assets/images/banner1.webp");
    background-size: cover;
    background-position: center;
    z-index: -1;
    filter: brightness(0.85) contrast(1.1) saturate(1.1);
    transition: all 1.2s ease;
    transform: scale(1.05);
  }

  &.is-loaded::before {
    transform: scale(1);
  }

  &::after {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.4),
      rgba(0, 0, 0, 0.2)
    );
    z-index: -1;
  }
}

.page-header {
  height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;
  color: #fff;
  margin: 0;
  padding: 0;
  overflow: hidden;

  .container {
    position: relative;
    z-index: 10;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
  }

  h1 {
    font-size: 48px;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }

  .slogan {
    font-size: 28px;
    font-weight: 600;
    background: linear-gradient(45deg, #fff, $water-blue);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 30px;
    letter-spacing: 2px;
  }

  .water-wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120'%3E%3Cpath d='M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z' fill='%23ffffff' fill-opacity='0.1'/%3E%3C/svg%3E")
      repeat-x;
    animation: wave 3s ease-in-out infinite;
  }
}

@keyframes wave {
  0%,
  100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-50px);
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  font-size: 32px;
  text-align: center;
  margin-bottom: 50px;
  color: $primary-blue;
  position: relative;
  font-weight: 600;

  &:after {
    content: "";
    position: absolute;
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, $accent-teal, $water-blue);
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 2px;
  }
}

.company-intro {
  padding: 80px 0;
  background: #fff;

  .intro-content {
    display: flex;
    align-items: center;

    .intro-text {
      flex: 1;
      padding-right: 50px;

      h2 {
        font-size: 28px;
        margin-bottom: 25px;
        color: $primary-blue;
        font-weight: 600;
      }

      p {
        font-size: 16px;
        line-height: 1.8;
        color: #555;
        margin-bottom: 20px;
        text-align: justify;
      }
    }

    .intro-image {
      flex: 1;
      height: 400px;
      background: linear-gradient(135deg, $accent-teal, $water-blue);
      border-radius: 16px;
      position: relative;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;

      .tech-elements {
        position: relative;
        width: 100%;
        height: 100%;

        .floating-icon {
          position: absolute;
          width: 60px;
          height: 60px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 24px;
          animation: float 3s ease-in-out infinite;
          animation-delay: var(--delay);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.3);

          &:nth-child(1) {
            top: 20%;
            left: 20%;
          }
          &:nth-child(2) {
            top: 30%;
            right: 20%;
          }
          &:nth-child(3) {
            bottom: 30%;
            left: 30%;
          }
          &:nth-child(4) {
            bottom: 20%;
            right: 30%;
          }
        }
      }
    }

    @media (max-width: 768px) {
      flex-direction: column;

      .intro-text {
        padding-right: 0;
        margin-bottom: 30px;
      }

      .intro-image {
        width: 100%;
      }
    }
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
// 资质荣誉样式
.certificates {
  padding: 80px 0;
  background: #fff;

  .cert-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 50px;

    .cert-item {
      background: linear-gradient(135deg, $bg-light, #fff);
      padding: 40px 30px;
      border-radius: 16px;
      text-align: center;
      border: 2px solid $accent-teal;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 12px 40px rgba(107, 144, 128, 0.2);
      }

      .cert-icon {
        width: 70px;
        height: 70px;
        background: $primary-blue;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        color: #fff;
        font-size: 28px;
      }

      h3 {
        font-size: 18px;
        color: $primary-blue;
        margin-bottom: 10px;
        font-weight: 600;
      }

      p {
        color: #666;
        font-size: 14px;
      }
    }
  }
}

.vision-mission {
  padding: 80px 0;
  background: linear-gradient(135deg, $primary-blue, $ocean-blue);
  text-align: center;
  color: #fff;

  .vision,
  .mission {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
    margin-bottom: 50px;

    h2 {
      font-size: 28px;
      margin-bottom: 20px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;

      .section-icon {
        margin-right: 10px;
        color: $water-blue;
        font-size: 28px;
      }
    }

    p {
      font-size: 18px;
      line-height: 1.7;
      color: rgba(255, 255, 255, 0.9);
    }
  }
}

.contact-us {
  padding: 80px 0;
  background: $bg-light;

  .contact-info {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 30px;

    .contact-item {
      display: flex;
      align-items: flex-start;
      flex: 1;
      min-width: 250px;
      background: #fff;
      padding: 30px;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(45, 90, 39, 0.1);
      transition: all 0.3s ease;
      border: 1px solid rgba(107, 144, 128, 0.2);

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 16px 48px rgba(45, 90, 39, 0.15);
        border-color: $accent-teal;
      }

      .contact-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, $accent-teal, $water-blue);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        font-size: 24px;
        color: #fff;
        flex-shrink: 0;
      }

      div {
        h3 {
          font-size: 18px;
          color: $primary-blue;
          margin-bottom: 8px;
          font-weight: 600;
        }

        p {
          font-size: 15px;
          color: #666;
          line-height: 1.6;
        }
      }

      @media (max-width: 768px) {
        min-width: 100%;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    h1 {
      font-size: 36px;
    }

    .slogan {
      font-size: 20px;
    }
  }

  .section-title {
    font-size: 28px;
  }

  .core-tech .tech-grid {
    grid-template-columns: 1fr;
  }

  .certificates .cert-grid {
    grid-template-columns: 1fr;
  }
}
</style>
