<script setup>
import { Icon } from "@iconify/vue";
import { ref, onMounted } from "vue";

defineOptions({
  name: "Solutions",
});

// 页面状态
const isLoaded = ref(false);

// 核心解决方案数据
const coreSolutions = ref([
  {
    icon: 'mdi:water-pump',
    category: '水务管理',
    title: '智慧水务综合管理平台',
    description: '集成供水、排水、污水处理全流程的智能化管理平台，实现水务运营的数字化转型',
    benefits: ['提升运营效率30%', '降低漏损率15%', '优化调度决策', '提高服务质量'],
    technologies: ['物联网', '大数据', 'GIS', 'AI算法'],
    projectCount: 120,
    clientCount: 50
  },
  {
    icon: 'mdi:weather-flood',
    category: '防汛预警',
    title: '智能防汛预警系统',
    description: '基于AI算法的洪水预警预报系统，提供精准的灾害预警和应急响应方案',
    benefits: ['预警提前24小时', '准确率提升40%', '减少灾害损失', '快速应急响应'],
    technologies: ['AI预测', '遥感监测', '气象分析', '数值模拟'],
    projectCount: 80,
    clientCount: 35
  },
  {
    icon: 'mdi:chart-multiple',
    category: '资源调度',
    title: '水资源优化调度平台',
    description: '基于大数据分析的水资源智能调配系统，实现区域水资源的精细化管理',
    benefits: ['节水效率提升25%', '调度精度提高', '成本降低20%', '环境效益显著'],
    technologies: ['优化算法', '数据分析', '调度模型', '智能控制'],
    projectCount: 95,
    clientCount: 42
  },
  {
    icon: 'mdi:monitor-dashboard',
    category: '智能监测',
    title: '水利工程安全监测',
    description: '覆盖大坝、闸站、堤防等水利工程的全方位安全监测预警系统',
    benefits: ['24小时监控', '预警及时准确', '运维成本降低', '安全系数提升'],
    technologies: ['传感网络', '结构分析', '安全评估', '预警算法'],
    projectCount: 150,
    clientCount: 60
  },
  {
    icon: 'mdi:leaf',
    category: '生态保护',
    title: '水生态环境监管平台',
    description: '水环境质量监测与生态保护综合管理平台，助力水生态文明建设',
    benefits: ['环境质量改善', '生态修复指导', '污染溯源追踪', '绿色发展支撑'],
    technologies: ['环境监测', '生态模型', '遥感分析', '数据融合'],
    projectCount: 70,
    clientCount: 28
  },
  {
    icon: 'mdi:city',
    category: '智慧城市',
    title: '城市水务数字孪生',
    description: '构建城市水务系统的数字孪生体，实现城市水务的智慧化管理',
    benefits: ['全域可视化', '精准仿真预测', '协同决策优化', '智慧运营管理'],
    technologies: ['数字孪生', '3D建模', '仿真计算', '虚实融合'],
    projectCount: 45,
    clientCount: 18
  }
]);

// 实施流程数据
const processSteps = ref([
  {
    icon: 'mdi:clipboard-text',
    title: '需求分析',
    description: '深入了解客户业务需求，制定详细的解决方案规划',
    details: ['业务调研分析', '技术可行性评估', '方案设计规划', '项目计划制定'],
    duration: '2-4周'
  },
  {
    icon: 'mdi:cog',
    title: '系统设计',
    description: '根据需求分析结果，设计系统架构和技术实现方案',
    details: ['系统架构设计', '数据库设计', '接口规范定义', '安全方案设计'],
    duration: '3-6周'
  },
  {
    icon: 'mdi:code-tags',
    title: '开发实施',
    description: '按照设计方案进行系统开发和功能实现',
    details: ['核心功能开发', '接口对接实现', '数据迁移处理', '系统集成测试'],
    duration: '8-16周'
  },
  {
    icon: 'mdi:test-tube',
    title: '测试验收',
    description: '全面测试系统功能，确保系统稳定可靠',
    details: ['功能测试验证', '性能压力测试', '安全漏洞检测', '用户验收测试'],
    duration: '2-4周'
  },
  {
    icon: 'mdi:rocket-launch',
    title: '上线部署',
    description: '系统正式上线运行，提供技术支持和培训',
    details: ['生产环境部署', '数据正式迁移', '用户培训指导', '试运行监控'],
    duration: '1-2周'
  },
  {
    icon: 'mdi:wrench',
    title: '运维支持',
    description: '提供持续的技术支持和系统维护服务',
    details: ['7×24小时监控', '故障快速响应', '系统优化升级', '技术支持服务'],
    duration: '长期'
  }
]);

// 成功案例数据
const successCases = ref([
  {
    title: '某省级水利厅智慧水利平台',
    category: '省级项目',
    description: '构建覆盖全省的智慧水利综合管理平台，实现水资源统一调度和智能化管理',
    image: '@/assets/images/case1.webp',
    achievements: [
      { value: '30%', label: '效率提升' },
      { value: '500+', label: '监测点' },
      { value: '24h', label: '实时监控' }
    ],
    technologies: ['大数据平台', 'AI算法', '物联网', 'GIS系统']
  },
  {
    title: '某市防汛指挥决策系统',
    category: '市级项目',
    description: '建设覆盖全市的防汛预警指挥系统，显著提升防汛应急响应能力',
    image: '@/assets/images/case2.webp',
    achievements: [
      { value: '6h', label: '预警提前' },
      { value: '95%', label: '准确率' },
      { value: '50%', label: '响应提速' }
    ],
    technologies: ['预警算法', '应急指挥', '移动应用', '视频监控']
  },
  {
    title: '某水库群智能调度系统',
    category: '工程项目',
    description: '构建多水库联合调度优化系统，实现水资源的精细化管理和优化配置',
    image: '@/assets/images/case3.webp',
    achievements: [
      { value: '20%', label: '节水效益' },
      { value: '15', label: '水库数量' },
      { value: '99%', label: '系统可用性' }
    ],
    technologies: ['调度算法', '数据融合', '优化模型', '智能控制']
  }
]);

// 事件处理
const handleSolutionDetail = (solution) => {
  console.log('查看解决方案详情:', solution);
};

const handleConsultation = () => {
  console.log('预约专家咨询');
};

const handleDemo = () => {
  console.log('观看演示视频');
};

// 页面加载动画
onMounted(() => {
  setTimeout(() => {
    isLoaded.value = true;
  }, 100);
});
</script>

<template>
  <div class="solutions" :class="{ 'loaded': isLoaded }">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="hero-particles">
          <div v-for="i in 15" :key="i" :class="`particle particle-${i}`"></div>
        </div>
        <div class="hero-network">
          <div v-for="i in 8" :key="i" :class="`network-node node-${i}`"></div>
          <svg class="network-lines" viewBox="0 0 100 100">
            <path class="network-path" d="M20,30 Q50,10 80,30" />
            <path class="network-path" d="M15,60 Q40,40 65,60" />
            <path class="network-path" d="M30,80 Q60,60 90,80" />
          </svg>
        </div>
      </div>

      <div class="container">
        <div class="hero-content">
          <div class="hero-badge fade-in-up">
            <Icon icon="mdi:lightbulb-on-outline" class="badge-icon" />
            <span>智慧水利数字化转型</span>
          </div>

          <h1 class="hero-title fade-in-up delay-1">
            <span class="title-line">全方位解决方案</span>
            <span class="title-line title-highlight">赋能智慧水利</span>
          </h1>

          <p class="hero-subtitle fade-in-up delay-2">
            基于物联网、大数据、AI等前沿技术，提供从规划设计到运维管理的<br>
            <strong>全生命周期数字化解决方案</strong>
          </p>

          <div class="hero-features fade-in-up delay-3">
            <div class="feature-item">
              <div class="feature-icon">
                <Icon icon="mdi:chart-timeline-variant" />
              </div>
              <div class="feature-text">
                <div class="feature-title">实时监控</div>
                <div class="feature-desc">7×24小时全天候监测</div>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <Icon icon="mdi:brain" />
              </div>
              <div class="feature-text">
                <div class="feature-title">智能分析</div>
                <div class="feature-desc">AI驱动的预测分析</div>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <Icon icon="mdi:shield-check" />
              </div>
              <div class="feature-text">
                <div class="feature-title">安全可靠</div>
                <div class="feature-desc">企业级安全保障</div>
              </div>
            </div>
          </div>
        </div>

        <div class="hero-visual fade-in-up delay-4">
          <div class="visual-container">
            <div class="solution-hub">
              <div class="hub-center">
                <Icon icon="mdi:water-circle" class="hub-icon" />
                <div class="hub-pulse"></div>
              </div>
              <div class="hub-orbits">
                <div class="orbit orbit-1">
                  <div class="orbit-item item-1">
                    <Icon icon="mdi:chart-line" />
                  </div>
                </div>
                <div class="orbit orbit-2">
                  <div class="orbit-item item-2">
                    <Icon icon="mdi:cloud-outline" />
                  </div>
                  <div class="orbit-item item-3">
                    <Icon icon="mdi:shield-check" />
                  </div>
                </div>
                <div class="orbit orbit-3">
                  <div class="orbit-item item-4">
                    <Icon icon="mdi:trending-up" />
                  </div>
                  <div class="orbit-item item-5">
                    <Icon icon="mdi:database" />
                  </div>
                  <div class="orbit-item item-6">
                    <Icon icon="mdi:cog" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心解决方案 -->
    <section class="core-solutions">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:puzzle" />
            <span>核心方案</span>
          </div>
          <h2 class="section-title">专业解决方案矩阵</h2>
          <p class="section-subtitle">覆盖水利行业全业务场景的专业化解决方案</p>
        </div>

        <div class="solutions-grid">
          <div
            v-for="(solution, index) in coreSolutions"
            :key="index"
            class="solution-card"
            @click="handleSolutionDetail(solution)"
          >
            <div class="card-header">
              <div class="solution-icon">
                <Icon :icon="solution.icon" />
              </div>
              <div class="solution-category">{{ solution.category }}</div>
            </div>

            <div class="card-content">
              <h3 class="solution-title">{{ solution.title }}</h3>
              <p class="solution-description">{{ solution.description }}</p>

              <div class="solution-benefits">
                <div class="benefits-title">核心价值</div>
                <div class="benefits-list">
                  <div class="benefit" v-for="benefit in solution.benefits" :key="benefit">
                    <Icon icon="mdi:check-circle" class="benefit-icon" />
                    <span>{{ benefit }}</span>
                  </div>
                </div>
              </div>

              <div class="solution-tech">
                <div class="tech-title">技术栈</div>
                <div class="tech-tags">
                  <span class="tech-tag" v-for="tech in solution.technologies" :key="tech">{{ tech }}</span>
                </div>
              </div>
            </div>

            <div class="card-footer">
              <div class="solution-metrics">
                <div class="metric">
                  <span class="metric-number">{{ solution.projectCount }}+</span>
                  <span class="metric-label">成功案例</span>
                </div>
                <div class="metric">
                  <span class="metric-number">{{ solution.clientCount }}+</span>
                  <span class="metric-label">客户数量</span>
                </div>
              </div>
              <button class="explore-btn">
                <span>了解详情</span>
                <Icon icon="mdi:arrow-right" class="btn-arrow" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 实施流程 -->
    <section class="implementation-process">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:cogs" />
            <span>实施流程</span>
          </div>
          <h2 class="section-title">专业实施保障</h2>
          <p class="section-subtitle">标准化流程确保项目成功交付</p>
        </div>

        <div class="process-timeline">
          <div class="timeline-track"></div>
          <div
            v-for="(step, index) in processSteps"
            :key="index"
            class="process-step"
            :class="`step-${index + 1}`"
          >
            <div class="step-marker">
              <div class="marker-inner">
                <Icon :icon="step.icon" />
              </div>
              <div class="marker-number">{{ String(index + 1).padStart(2, '0') }}</div>
            </div>

            <div class="step-content">
              <h3 class="step-title">{{ step.title }}</h3>
              <p class="step-description">{{ step.description }}</p>

              <div class="step-details">
                <div class="detail-item" v-for="detail in step.details" :key="detail">
                  <Icon icon="mdi:chevron-right" class="detail-icon" />
                  <span>{{ detail }}</span>
                </div>
              </div>

              <div class="step-duration">
                <Icon icon="mdi:clock-outline" />
                <span>{{ step.duration }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 成功案例 -->
    <section class="success-cases">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:trophy" />
            <span>成功案例</span>
          </div>
          <h2 class="section-title">客户成功故事</h2>
          <p class="section-subtitle">真实案例见证解决方案价值</p>
        </div>

        <div class="cases-showcase">
          <div
            v-for="(caseItem, index) in successCases"
            :key="index"
            class="case-card"
          >
            <div class="case-visual">
              <div class="case-image">
                <img :src="caseItem.image" :alt="caseItem.title" />
              </div>
              <div class="case-overlay">
                <div class="case-category">{{ caseItem.category }}</div>
              </div>
            </div>

            <div class="case-content">
              <h3 class="case-title">{{ caseItem.title }}</h3>
              <p class="case-description">{{ caseItem.description }}</p>

              <div class="case-achievements">
                <div class="achievement" v-for="achievement in caseItem.achievements" :key="achievement.label">
                  <div class="achievement-number">{{ achievement.value }}</div>
                  <div class="achievement-label">{{ achievement.label }}</div>
                </div>
              </div>

              <div class="case-tech">
                <span class="tech-item" v-for="tech in caseItem.technologies" :key="tech">{{ tech }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA 区域 -->
    <section class="cta-section">
      <div class="cta-background">
        <div class="cta-overlay"></div>
        <div class="cta-grid">
          <div v-for="i in 30" :key="i" class="grid-item"></div>
        </div>
      </div>

      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <div class="cta-badge">
              <Icon icon="mdi:rocket-launch" />
              <span>开始您的数字化之旅</span>
            </div>
            <h2 class="cta-title">准备好迎接智慧水利的未来了吗？</h2>
            <p class="cta-subtitle">
              我们的专业团队将为您提供<strong>端到端的解决方案咨询</strong>和<strong>定制化实施服务</strong><br>
              让技术真正为您的业务创造价值
            </p>

            <div class="cta-benefits">
              <div class="benefit-item">
                <Icon icon="mdi:account-tie" />
                <span>专家1对1咨询</span>
              </div>
              <div class="benefit-item">
                <Icon icon="mdi:file-document-outline" />
                <span>免费方案评估</span>
              </div>
              <div class="benefit-item">
                <Icon icon="mdi:clock-fast" />
                <span>快速响应服务</span>
              </div>
            </div>
          </div>

          <div class="cta-actions">
            <button class="btn-primary large" @click="handleConsultation">
              <Icon icon="mdi:account-tie" class="btn-icon" />
              <span>预约专家咨询</span>
              <Icon icon="mdi:arrow-right" class="btn-arrow" />
            </button>
            <button class="btn-secondary large" @click="handleDemo">
              <Icon icon="mdi:play-circle" class="btn-icon" />
              <span>观看演示视频</span>
            </button>

            <div class="contact-info">
              <div class="contact-item">
                <Icon icon="mdi:phone" />
                <span>************</span>
              </div>
              <div class="contact-item">
                <Icon icon="mdi:email" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@use "@/assets/styles/variables.scss" as *;

.solutions {
  min-height: 100vh;
  color: var(--gray-800);
  overflow-x: hidden;

  // 页面加载动画
  opacity: 0;
  transition: opacity var(--duration-700) var(--ease-out);

  &.loaded {
    opacity: 1;

    .fade-in-up {
      opacity: 1;
      transform: translateY(0);

      &.delay-1 { animation-delay: 0.1s; }
      &.delay-2 { animation-delay: 0.2s; }
      &.delay-3 { animation-delay: 0.3s; }
      &.delay-4 { animation-delay: 0.4s; }
    }
  }
}

// 通用动画类
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s var(--ease-smooth) forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 通用容器
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);

  @media (max-width: 768px) {
    padding: 0 var(--space-4);
  }
}

// 英雄区域
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;

  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-depth);
    z-index: -1;
  }

  .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--primary-alpha-90) 0%,
      var(--corporate-blue) 100%
    );
    z-index: -1;
  }

  .hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;

    .particle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: var(--electric-blue);
      border-radius: 50%;
      opacity: 0.6;

      &.particle-1 { top: 20%; left: 10%; animation: float 6s infinite 0s; }
      &.particle-2 { top: 30%; left: 80%; animation: float 8s infinite 1s; }
      &.particle-3 { top: 60%; left: 15%; animation: float 7s infinite 2s; }
      &.particle-4 { top: 80%; left: 70%; animation: float 9s infinite 3s; }
      &.particle-5 { top: 40%; left: 90%; animation: float 6s infinite 4s; }
      &.particle-6 { top: 70%; left: 25%; animation: float 8s infinite 5s; }
      &.particle-7 { top: 15%; left: 60%; animation: float 7s infinite 1.5s; }
      &.particle-8 { top: 85%; left: 40%; animation: float 9s infinite 2.5s; }
      &.particle-9 { top: 50%; left: 5%; animation: float 6s infinite 3.5s; }
      &.particle-10 { top: 25%; left: 95%; animation: float 8s infinite 4.5s; }
      &.particle-11 { top: 75%; left: 55%; animation: float 7s infinite 0.5s; }
      &.particle-12 { top: 35%; left: 30%; animation: float 9s infinite 1.8s; }
      &.particle-13 { top: 65%; left: 85%; animation: float 6s infinite 2.8s; }
      &.particle-14 { top: 45%; left: 75%; animation: float 8s infinite 3.8s; }
      &.particle-15 { top: 55%; left: 45%; animation: float 7s infinite 4.8s; }
    }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
    25% { transform: translateY(-20px) rotate(90deg); opacity: 1; }
    50% { transform: translateY(-10px) rotate(180deg); opacity: 0.8; }
    75% { transform: translateY(-30px) rotate(270deg); opacity: 1; }
  }

  .hero-network {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;

    .network-node {
      position: absolute;
      width: 8px;
      height: 8px;
      background: var(--neon-cyan);
      border-radius: 50%;
      box-shadow: 0 0 20px var(--neon-cyan);

      &.node-1 { top: 25%; left: 20%; animation: pulse 3s infinite 0s; }
      &.node-2 { top: 40%; left: 75%; animation: pulse 3s infinite 0.5s; }
      &.node-3 { top: 65%; left: 30%; animation: pulse 3s infinite 1s; }
      &.node-4 { top: 80%; left: 80%; animation: pulse 3s infinite 1.5s; }
      &.node-5 { top: 15%; left: 85%; animation: pulse 3s infinite 2s; }
      &.node-6 { top: 55%; left: 10%; animation: pulse 3s infinite 2.5s; }
      &.node-7 { top: 35%; left: 50%; animation: pulse 3s infinite 1.2s; }
      &.node-8 { top: 70%; left: 65%; animation: pulse 3s infinite 1.8s; }
    }

    .network-lines {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0.3;

      .network-path {
        fill: none;
        stroke: var(--electric-blue);
        stroke-width: 0.5;
        stroke-dasharray: 5,5;
        animation: dash 20s linear infinite;
      }
    }
  }

  @keyframes pulse {
    0%, 100% { opacity: 0.4; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.5); }
  }

  @keyframes dash {
    to { stroke-dashoffset: -100; }
  }

  .container {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: var(--space-12);
      text-align: center;
    }
  }

  .hero-content {
    color: var(--white);

    .hero-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--white-alpha-20);
      border: 1px solid var(--white-alpha-30);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      margin-bottom: var(--space-6);
      @include glass-morphism(0.1, 16px, 0.2);

      .badge-icon {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .hero-title {
      font-size: var(--text-5xl);
      font-weight: var(--font-bold);
      line-height: var(--leading-tight);
      margin-bottom: var(--space-6);

      @media (max-width: 768px) {
        font-size: var(--text-4xl);
      }

      .title-line {
        display: block;

        &.title-highlight {
          @include gradient-text(var(--gradient-neon));
        }
      }
    }

    .hero-subtitle {
      font-size: var(--text-lg);
      line-height: var(--leading-relaxed);
      opacity: 0.9;
      margin-bottom: var(--space-8);

      strong {
        color: var(--electric-blue);
        font-weight: var(--font-bold);
      }
    }

    .hero-features {
      display: flex;
      gap: var(--space-8);
      margin-bottom: var(--space-8);

      @media (max-width: 640px) {
        flex-direction: column;
        gap: var(--space-4);
      }

      .feature-item {
        display: flex;
        align-items: center;
        gap: var(--space-3);

        .feature-icon {
          width: 50px;
          height: 50px;
          background: var(--gradient-glass);
          @include glass-morphism(0.2, 12px, 0.3);
          border-radius: var(--radius-xl);
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--electric-blue);
          font-size: var(--text-xl);
        }

        .feature-text {
          .feature-title {
            font-size: var(--text-base);
            font-weight: var(--font-bold);
            margin-bottom: var(--space-1);
          }

          .feature-desc {
            font-size: var(--text-sm);
            opacity: 0.8;
          }
        }
      }
    }
  }

  .hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;

    .visual-container {
      position: relative;
      width: 400px;
      height: 400px;

      @media (max-width: 768px) {
        width: 300px;
        height: 300px;
      }
    }

    .solution-hub {
      position: relative;
      width: 100%;
      height: 100%;

      .hub-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100px;
        height: 100px;
        background: var(--gradient-primary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: var(--glow-primary);
        z-index: 3;

        .hub-icon {
          font-size: var(--text-4xl);
          color: var(--white);
        }

        .hub-pulse {
          position: absolute;
          top: -20px;
          left: -20px;
          width: 140px;
          height: 140px;
          border: 2px solid var(--electric-blue);
          border-radius: 50%;
          animation: hubPulse 3s infinite;
        }
      }

      .hub-orbits {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        .orbit {
          position: absolute;
          top: 50%;
          left: 50%;
          border: 1px solid var(--primary-alpha-30);
          border-radius: 50%;
          transform: translate(-50%, -50%);

          &.orbit-1 {
            width: 180px;
            height: 180px;
            animation: rotate 20s linear infinite;
          }

          &.orbit-2 {
            width: 260px;
            height: 260px;
            animation: rotate 30s linear infinite reverse;
          }

          &.orbit-3 {
            width: 340px;
            height: 340px;
            animation: rotate 40s linear infinite;
          }

          .orbit-item {
            position: absolute;
            width: 40px;
            height: 40px;
            background: var(--gradient-glass);
            @include glass-morphism(0.2, 12px, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--electric-blue);
            font-size: var(--text-lg);
            box-shadow: var(--glow-neon);

            &.item-1 {
              top: -20px;
              left: 50%;
              transform: translateX(-50%);
            }

            &.item-2 {
              top: -20px;
              left: 50%;
              transform: translateX(-50%);
            }

            &.item-3 {
              bottom: -20px;
              left: 50%;
              transform: translateX(-50%);
            }

            &.item-4 {
              top: -20px;
              left: 50%;
              transform: translateX(-50%);
            }

            &.item-5 {
              right: -20px;
              top: 50%;
              transform: translateY(-50%);
            }

            &.item-6 {
              bottom: -20px;
              left: 50%;
              transform: translateX(-50%);
            }
          }
        }
      }
    }
  }

  @keyframes hubPulse {
    0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 0.2; transform: translate(-50%, -50%) scale(1.2); }
  }

  @keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
  }
}

// 核心解决方案区域
.core-solutions {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--gradient-subtle);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .solutions-grid {
    @include responsive-grid(auto-fit, 380px, var(--space-8));
  }

  .solution-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    cursor: pointer;
    transition: var(--transition-all);

    @include card-hover(1.02, -6px);

    .card-header {
      position: relative;
      padding: var(--space-8);
      background: var(--gradient-subtle);
      text-align: center;

      .solution-icon {
        width: 80px;
        height: 80px;
        background: var(--gradient-primary);
        border-radius: var(--radius-2xl);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--space-4);
        color: var(--white);
        font-size: var(--text-3xl);
        box-shadow: var(--glow-primary);
        transition: var(--transition-all);
      }

      .solution-category {
        display: inline-block;
        padding: var(--space-1) var(--space-3);
        background: var(--primary-alpha-20);
        color: var(--primary-blue);
        border-radius: var(--radius-2xl);
        font-size: var(--text-xs);
        font-weight: var(--font-bold);
      }
    }

    .card-content {
      padding: var(--space-6);

      .solution-title {
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--gray-900);
        margin-bottom: var(--space-3);
        line-height: var(--leading-tight);
      }

      .solution-description {
        color: var(--gray-600);
        line-height: var(--leading-relaxed);
        margin-bottom: var(--space-6);
        font-size: var(--text-sm);
      }

      .solution-benefits {
        margin-bottom: var(--space-6);

        .benefits-title {
          font-size: var(--text-sm);
          font-weight: var(--font-bold);
          color: var(--gray-800);
          margin-bottom: var(--space-3);
        }

        .benefits-list {
          display: flex;
          flex-direction: column;
          gap: var(--space-2);

          .benefit {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: var(--text-sm);
            color: var(--gray-700);

            .benefit-icon {
              color: var(--success-green);
              font-size: var(--text-sm);
              flex-shrink: 0;
            }
          }
        }
      }

      .solution-tech {
        margin-bottom: var(--space-6);

        .tech-title {
          font-size: var(--text-sm);
          font-weight: var(--font-bold);
          color: var(--gray-800);
          margin-bottom: var(--space-3);
        }

        .tech-tags {
          display: flex;
          flex-wrap: wrap;
          gap: var(--space-2);

          .tech-tag {
            background: var(--crystal-blue);
            color: var(--primary-blue);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-2xl);
            font-size: var(--text-xs);
            font-weight: var(--font-medium);
          }
        }
      }
    }

    .card-footer {
      padding: 0 var(--space-6) var(--space-6);
      display: flex;
      justify-content: space-between;
      align-items: center;

      .solution-metrics {
        display: flex;
        gap: var(--space-6);

        .metric {
          text-align: center;

          .metric-number {
            display: block;
            font-size: var(--text-lg);
            font-weight: var(--font-bold);
            color: var(--primary-blue);
          }

          .metric-label {
            font-size: var(--text-xs);
            color: var(--gray-500);
          }
        }
      }

      .explore-btn {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-2) var(--space-4);
        background: var(--gradient-primary);
        color: var(--white);
        border: none;
        border-radius: var(--radius-lg);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        cursor: pointer;
        transition: var(--transition-all);

        .btn-arrow {
          font-size: var(--text-sm);
          transition: var(--transition-all);
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-hover);

          .btn-arrow {
            transform: translateX(2px);
          }
        }
      }
    }

    &:hover {
      .card-header .solution-icon {
        transform: scale(1.1);
      }
    }
  }
}

// 实施流程区域
.implementation-process {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--white);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .process-timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;

    .timeline-track {
      position: absolute;
      left: 50%;
      top: 0;
      bottom: 0;
      width: 2px;
      background: var(--gradient-primary);
      transform: translateX(-50%);
      z-index: 1;

      @media (max-width: 768px) {
        left: 30px;
      }
    }

    .process-step {
      position: relative;
      display: flex;
      align-items: flex-start;
      margin-bottom: var(--space-16);
      z-index: 2;

      @media (max-width: 768px) {
        margin-left: var(--space-16);
      }

      &:nth-child(even) {
        flex-direction: row-reverse;

        @media (max-width: 768px) {
          flex-direction: row;
        }

        .step-content {
          text-align: right;
          margin-right: var(--space-8);
          margin-left: 0;

          @media (max-width: 768px) {
            text-align: left;
            margin-right: 0;
            margin-left: var(--space-4);
          }
        }
      }

      .step-marker {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 3;

        .marker-inner {
          width: 80px;
          height: 80px;
          background: var(--gradient-primary);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--white);
          font-size: var(--text-2xl);
          box-shadow: var(--glow-primary);
          border: 4px solid var(--white);
        }

        .marker-number {
          position: absolute;
          bottom: -30px;
          background: var(--white);
          color: var(--primary-blue);
          padding: var(--space-1) var(--space-2);
          border-radius: var(--radius-lg);
          font-size: var(--text-xs);
          font-weight: var(--font-bold);
          box-shadow: var(--shadow-sm);
        }
      }

      .step-content {
        flex: 1;
        max-width: 300px;
        margin-left: var(--space-8);

        @media (max-width: 768px) {
          margin-left: var(--space-4);
          max-width: none;
        }

        .step-title {
          font-size: var(--text-xl);
          font-weight: var(--font-bold);
          color: var(--gray-900);
          margin-bottom: var(--space-3);
        }

        .step-description {
          color: var(--gray-600);
          line-height: var(--leading-relaxed);
          margin-bottom: var(--space-4);
          font-size: var(--text-base);
        }

        .step-details {
          margin-bottom: var(--space-4);

          .detail-item {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            margin-bottom: var(--space-2);
            font-size: var(--text-sm);
            color: var(--gray-700);

            .detail-icon {
              color: var(--primary-blue);
              font-size: var(--text-sm);
              flex-shrink: 0;
            }
          }
        }

        .step-duration {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          padding: var(--space-2) var(--space-3);
          background: var(--crystal-blue);
          color: var(--primary-blue);
          border-radius: var(--radius-lg);
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          width: fit-content;

          svg {
            font-size: var(--text-base);
          }
        }
      }
    }
  }
}

// 成功案例区域
.success-cases {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--gradient-subtle);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .cases-showcase {
    @include responsive-grid(auto-fit, 380px, var(--space-8));
  }

  .case-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-all);

    @include card-hover(1.02, -6px);

    .case-visual {
      position: relative;
      height: 200px;
      overflow: hidden;

      .case-image {
        width: 100%;
        height: 100%;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: var(--transition-all);
        }
      }

      .case-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          to bottom,
          transparent 0%,
          var(--primary-alpha-80) 100%
        );
        display: flex;
        align-items: flex-end;
        padding: var(--space-4);

        .case-category {
          background: var(--white);
          color: var(--primary-blue);
          padding: var(--space-1) var(--space-3);
          border-radius: var(--radius-lg);
          font-size: var(--text-xs);
          font-weight: var(--font-bold);
        }
      }
    }

    .case-content {
      padding: var(--space-6);

      .case-title {
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--gray-900);
        margin-bottom: var(--space-3);
        line-height: var(--leading-tight);
      }

      .case-description {
        color: var(--gray-600);
        line-height: var(--leading-relaxed);
        margin-bottom: var(--space-6);
        font-size: var(--text-sm);
      }

      .case-achievements {
        display: flex;
        justify-content: space-between;
        margin-bottom: var(--space-6);

        .achievement {
          text-align: center;

          .achievement-number {
            font-size: var(--text-2xl);
            font-weight: var(--font-bold);
            color: var(--primary-blue);
            display: block;
          }

          .achievement-label {
            font-size: var(--text-xs);
            color: var(--gray-500);
            margin-top: var(--space-1);
          }
        }
      }

      .case-tech {
        display: flex;
        flex-wrap: wrap;
        gap: var(--space-2);

        .tech-item {
          background: var(--crystal-blue);
          color: var(--primary-blue);
          padding: var(--space-1) var(--space-3);
          border-radius: var(--radius-2xl);
          font-size: var(--text-xs);
          font-weight: var(--font-medium);
        }
      }
    }

    &:hover {
      .case-visual .case-image img {
        transform: scale(1.05);
      }
    }
  }
}

// CTA 区域
.cta-section {
  position: relative;
  @include section-padding(var(--space-20), var(--space-20));
  overflow: hidden;

  .cta-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
  }

  .cta-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--primary-alpha-90) 0%,
      var(--corporate-blue) 100%
    );
  }

  .cta-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: repeat(5, 1fr);
    opacity: 0.1;

    .grid-item {
      border: 1px solid var(--white-alpha-20);
      transition: var(--transition-all);

      &:hover {
        background: var(--white-alpha-10);
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
  }

  .cta-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--space-12);
    align-items: center;
    color: var(--white);

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      text-align: center;
      gap: var(--space-8);
    }

    .cta-text {
      .cta-badge {
        display: inline-flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-2) var(--space-4);
        background: var(--white-alpha-20);
        border: 1px solid var(--white-alpha-30);
        border-radius: var(--radius-2xl);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        margin-bottom: var(--space-6);
        @include glass-morphism(0.1, 16px, 0.2);

        svg {
          font-size: var(--text-lg);
          color: var(--electric-blue);
        }
      }

      .cta-title {
        font-size: var(--text-4xl);
        font-weight: var(--font-bold);
        margin-bottom: var(--space-6);
        line-height: var(--leading-tight);

        @media (max-width: 768px) {
          font-size: var(--text-3xl);
        }
      }

      .cta-subtitle {
        font-size: var(--text-lg);
        line-height: var(--leading-relaxed);
        opacity: 0.9;
        margin-bottom: var(--space-8);

        strong {
          color: var(--electric-blue);
          font-weight: var(--font-bold);
        }
      }

      .cta-benefits {
        display: flex;
        gap: var(--space-6);
        margin-bottom: var(--space-8);

        @media (max-width: 640px) {
          flex-direction: column;
          gap: var(--space-4);
        }

        .benefit-item {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          font-size: var(--text-sm);
          font-weight: var(--font-medium);

          svg {
            font-size: var(--text-lg);
            color: var(--electric-blue);
          }
        }
      }
    }

    .cta-actions {
      display: flex;
      flex-direction: column;
      gap: var(--space-4);
      align-items: flex-end;

      @media (max-width: 968px) {
        align-items: center;
      }

      button {
        display: flex;
        align-items: center;
        gap: var(--space-3);
        padding: var(--space-4) var(--space-6);
        border-radius: var(--radius-2xl);
        font-size: var(--text-base);
        font-weight: var(--font-medium);
        cursor: pointer;
        transition: var(--transition-all);
        border: none;
        white-space: nowrap;

        &.large {
          padding: var(--space-5) var(--space-8);
          font-size: var(--text-lg);
        }

        .btn-icon {
          font-size: var(--text-xl);
        }

        .btn-arrow {
          font-size: var(--text-lg);
          transition: var(--transition-all);
        }

        &.btn-primary {
          background: var(--white);
          color: var(--primary-blue);

          &:hover {
            background: var(--crystal-blue);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);

            .btn-arrow {
              transform: translateX(4px);
            }
          }
        }

        &.btn-secondary {
          background: transparent;
          color: var(--white);
          border: 2px solid var(--white);

          &:hover {
            background: var(--white);
            color: var(--primary-blue);
            transform: translateY(-2px);
          }
        }
      }

      .contact-info {
        display: flex;
        flex-direction: column;
        gap: var(--space-2);
        margin-top: var(--space-4);

        .contact-item {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          font-size: var(--text-sm);
          color: rgba(255, 255, 255, 0.8);

          svg {
            font-size: var(--text-base);
            color: var(--electric-blue);
          }
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 968px) {
  .hero-section {
    min-height: 80vh;

    .hero-title {
      font-size: var(--text-4xl);
    }

    .hero-subtitle {
      font-size: var(--text-base);
    }

    .hero-features {
      flex-direction: column;
      gap: var(--space-4);
    }
  }

  .core-solutions,
  .success-cases {
    .solutions-grid,
    .cases-showcase {
      grid-template-columns: 1fr;
    }
  }

  .implementation-process {
    .process-timeline {
      .process-step {
        &:nth-child(even) {
          flex-direction: row;

          .step-content {
            text-align: left;
            margin-right: 0;
            margin-left: var(--space-4);
          }
        }
      }
    }
  }
}

@media (max-width: 640px) {
  .hero-section {
    .hero-title {
      font-size: var(--text-3xl);
    }

    .hero-visual .visual-container {
      width: 250px;
      height: 250px;
    }
  }

  .core-solutions,
  .implementation-process,
  .success-cases,
  .cta-section {
    .section-header .section-title {
      font-size: var(--text-3xl);
    }
  }

  .implementation-process {
    .process-timeline {
      .process-step {
        .step-marker .marker-inner {
          width: 60px;
          height: 60px;
          font-size: var(--text-xl);
        }
      }
    }
  }
}
</style>
